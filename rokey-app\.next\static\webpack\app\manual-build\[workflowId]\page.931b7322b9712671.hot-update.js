"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/ToolNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToolNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* harmony import */ var _lib_oauth_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/oauth/config */ \"(app-pages-browser)/./src/lib/oauth/config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ToolNode(param) {\n    let { data, onUpdate } = param;\n    const config = data.config;\n    const toolType = config === null || config === void 0 ? void 0 : config.toolType;\n    const toolIcon = toolType ? _lib_oauth_config__WEBPACK_IMPORTED_MODULE_2__.TOOL_ICONS[toolType] : '🔧';\n    const toolName = toolType ? _lib_oauth_config__WEBPACK_IMPORTED_MODULE_2__.TOOL_DISPLAY_NAMES[toolType] : 'External Tool';\n    const connectionStatus = (config === null || config === void 0 ? void 0 : config.connectionStatus) || 'disconnected';\n    const isAuthenticated = (config === null || config === void 0 ? void 0 : config.isAuthenticated) || false;\n    const getStatusColor = ()=>{\n        if (isAuthenticated && connectionStatus === 'connected') {\n            return 'text-green-400';\n        }\n        switch(connectionStatus){\n            case 'expired':\n            case 'revoked':\n            case 'error':\n                return 'text-red-400';\n            default:\n                return 'text-yellow-400';\n        }\n    };\n    const getStatusText = ()=>{\n        if (isAuthenticated && connectionStatus === 'connected') {\n            return 'Connected';\n        }\n        switch(connectionStatus){\n            case 'expired':\n                return 'Expired';\n            case 'revoked':\n                return 'Revoked';\n            case 'error':\n                return 'Error';\n            default:\n                return 'Not Connected';\n        }\n    };\n    const getStatusIcon = ()=>{\n        if (isAuthenticated && connectionStatus === 'connected') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-4 w-4 text-green-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 53,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4 text-yellow-400\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n            lineNumber: 55,\n            columnNumber: 12\n        }, this);\n    };\n    const handleConfigUpdate = (newConfig)=>{\n        if (onUpdate && data.id) {\n            onUpdate(data.id, {\n                ...data,\n                config: newConfig\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            data: data,\n            icon: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"#06b6d4\",\n            hasInput: false,\n            hasOutput: true,\n            onDoubleClick: ()=>{},\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: toolType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: toolIcon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-white\",\n                                            children: toolName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: getStatusIcon()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs \".concat(getStatusColor(), \" flex items-center gap-1\"),\n                            children: [\n                                getStatusText(),\n                                (config === null || config === void 0 ? void 0 : config.providerUserEmail) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400\",\n                                    children: [\n                                        \"• \",\n                                        config.providerUserEmail\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 15\n                        }, this),\n                        (config === null || config === void 0 ? void 0 : config.timeout) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Timeout: \",\n                                config.timeout,\n                                \"s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 17\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs px-2 py-1 rounded \".concat(isAuthenticated && connectionStatus === 'connected' ? 'text-green-300 bg-green-900/20' : 'text-yellow-300 bg-yellow-900/20'),\n                            children: isAuthenticated && connectionStatus === 'connected' ? '✓ Tool connected' : '⚠️ Needs connection'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-300\",\n                            children: \"External Tool Integration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Connect to external services like Google Drive, Notion, Gmail, and more.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                            children: \"⚠️ Needs configuration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_c = ToolNode;\nvar _c;\n$RefreshReg$(_c, \"ToolNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx\n"));

/***/ })

});