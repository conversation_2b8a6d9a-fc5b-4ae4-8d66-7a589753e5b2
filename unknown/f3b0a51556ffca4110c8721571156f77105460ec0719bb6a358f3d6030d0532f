'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  PlusIcon, 
  FolderIcon, 
  DocumentDuplicateIcon,
  PlayIcon,
  Cog6ToothIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import { ManualBuildWorkflow, WorkflowTemplate } from '@/types/manualBuild';

interface WorkflowCardProps {
  workflow: ManualBuildWorkflow;
  onEdit: (id: string) => void;
  onDuplicate: (id: string) => void;
  onDelete: (id: string) => void;
}

function WorkflowCard({ workflow, onEdit, onDuplicate, onDelete }: WorkflowCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2 }}
      className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 hover:border-[#ff6b35]/30 transition-all duration-300 group"
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-[#ff6b35] transition-colors">
            {workflow.name}
          </h3>
          <p className="text-gray-400 text-sm line-clamp-2">
            {workflow.description || 'No description provided'}
          </p>
        </div>
        <div className="flex items-center gap-2 ml-4">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            workflow.is_active 
              ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
              : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
          }`}>
            {workflow.is_active ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
        <span>{workflow.nodes.length} nodes</span>
        <span>Updated {new Date(workflow.updated_at).toLocaleDateString()}</span>
      </div>

      <div className="flex items-center gap-2">
        <button
          onClick={() => onEdit(workflow.id)}
          className="flex-1 bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"
        >
          <Cog6ToothIcon className="w-4 h-4" />
          Edit
        </button>
        <button
          onClick={() => onDuplicate(workflow.id)}
          className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors duration-200"
          title="Duplicate"
        >
          <DocumentDuplicateIcon className="w-4 h-4" />
        </button>
        <button
          className="bg-green-600 hover:bg-green-500 text-white px-3 py-2 rounded-lg transition-colors duration-200"
          title="Test in Playground"
        >
          <PlayIcon className="w-4 h-4" />
        </button>
      </div>
    </motion.div>
  );
}

interface TemplateCardProps {
  template: WorkflowTemplate;
  onUse: (id: string) => void;
}

function TemplateCard({ template, onUse }: TemplateCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2 }}
      className="bg-gradient-to-br from-blue-900/40 to-blue-800/20 backdrop-blur-sm border border-blue-700/50 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 group"
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-lg font-semibold text-white group-hover:text-blue-400 transition-colors">
              {template.name}
            </h3>
            {template.is_official && (
              <span className="bg-blue-500/20 text-blue-400 border border-blue-500/30 px-2 py-0.5 rounded-full text-xs font-medium">
                Official
              </span>
            )}
          </div>
          <p className="text-gray-400 text-sm line-clamp-2">
            {template.description}
          </p>
        </div>
      </div>

      <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
        <span className="bg-gray-700/50 px-2 py-1 rounded text-xs">{template.category}</span>
        <div className="flex items-center gap-4">
          <span>⭐ {template.rating.toFixed(1)}</span>
          <span>↓ {template.download_count}</span>
        </div>
      </div>

      <button
        onClick={() => onUse(template.id)}
        className="w-full bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
      >
        Use Template
      </button>
    </motion.div>
  );
}

export default function ManualBuildPage() {
  const router = useRouter();
  const [workflows, setWorkflows] = useState<ManualBuildWorkflow[]>([]);
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'workflows' | 'templates'>('workflows');

  useEffect(() => {
    loadWorkflows();
    loadTemplates();
  }, []);

  const loadWorkflows = async () => {
    try {
      const response = await fetch('/api/workflows');
      if (response.ok) {
        const data = await response.json();
        setWorkflows(data.workflows || []);
      } else {
        console.error('Failed to load workflows:', response.statusText);
      }
    } catch (error) {
      console.error('Failed to load workflows:', error);
    }
  };

  const loadTemplates = async () => {
    try {
      // TODO: Implement API call to fetch templates
      setTemplates([]);
      setLoading(false);
    } catch (error) {
      console.error('Failed to load templates:', error);
      setLoading(false);
    }
  };

  const handleCreateNew = () => {
    router.push('/manual-build/new');
  };

  const handleEditWorkflow = (id: string) => {
    router.push(`/manual-build/${id}`);
  };

  const handleDuplicateWorkflow = async (id: string) => {
    // TODO: Implement workflow duplication
    console.log('Duplicate workflow:', id);
  };

  const handleDeleteWorkflow = async (id: string) => {
    if (!confirm('Are you sure you want to delete this workflow? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/workflows?id=${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Reload workflows
        loadWorkflows();
        alert('Workflow deleted successfully');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to delete workflow');
      }
    } catch (error) {
      console.error('Failed to delete workflow:', error);
      alert(`Failed to delete workflow: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleUseTemplate = (templateId: string) => {
    router.push(`/manual-build/new?template=${templateId}`);
  };

  const filteredWorkflows = workflows.filter(workflow =>
    workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    workflow.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredTemplates = templates.filter(template =>
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-[#040716] flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#040716] p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Manual Build</h1>
            <p className="text-gray-400">Create custom AI workflows with visual node-based editor</p>
          </div>
          <button
            onClick={handleCreateNew}
            className="bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
          >
            <PlusIcon className="w-5 h-5" />
            Create New Workflow
          </button>
        </div>

        {/* Search and Tabs */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search workflows and templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-gray-800/50 border border-gray-700/50 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:border-[#ff6b35]/50 transition-colors w-80"
              />
            </div>
          </div>
          
          <div className="flex bg-gray-800/50 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('workflows')}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                activeTab === 'workflows'
                  ? 'bg-[#ff6b35] text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <FolderIcon className="w-4 h-4 inline mr-2" />
              My Workflows
            </button>
            <button
              onClick={() => setActiveTab('templates')}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                activeTab === 'templates'
                  ? 'bg-[#ff6b35] text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <DocumentDuplicateIcon className="w-4 h-4 inline mr-2" />
              Templates
            </button>
          </div>
        </div>

        {/* Content */}
        {activeTab === 'workflows' ? (
          <div>
            {filteredWorkflows.length === 0 ? (
              <div className="text-center py-12">
                <FolderIcon className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">No workflows yet</h3>
                <p className="text-gray-400 mb-6">Create your first visual workflow to get started</p>
                <button
                  onClick={handleCreateNew}
                  className="bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 inline-flex items-center gap-2"
                >
                  <PlusIcon className="w-5 h-5" />
                  Create New Workflow
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredWorkflows.map((workflow) => (
                  <WorkflowCard
                    key={workflow.id}
                    workflow={workflow}
                    onEdit={handleEditWorkflow}
                    onDuplicate={handleDuplicateWorkflow}
                    onDelete={handleDeleteWorkflow}
                  />
                ))}
              </div>
            )}
          </div>
        ) : (
          <div>
            {filteredTemplates.length === 0 ? (
              <div className="text-center py-12">
                <DocumentDuplicateIcon className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">No templates found</h3>
                <p className="text-gray-400">Try adjusting your search or check back later</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredTemplates.map((template) => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    onUse={handleUseTemplate}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
