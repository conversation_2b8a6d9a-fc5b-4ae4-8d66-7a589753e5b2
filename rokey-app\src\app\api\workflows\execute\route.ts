/**
 * Workflow Execution API
 * Allows executing workflows via persistent API keys (similar to model API)
 */

import { NextRequest, NextResponse } from 'next/server';
import { workflowPersistence } from '@/lib/workflow/WorkflowPersistence';
import { WorkflowExecutor } from '@/lib/workflow/WorkflowExecutor';
import { workflowMonitor } from '@/lib/workflow/WorkflowExecutionMonitor';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    // Get API key from header
    const apiKey = request.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key required in X-API-Key header' },
        { status: 401 }
      );
    }

    // Validate API key and get workflow info
    const validation = await workflowPersistence.validateWorkflowAPIKey(apiKey);
    if (!validation.valid || !validation.workflowId || !validation.userId) {
      return NextResponse.json(
        { error: 'Invalid or expired API key' },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();
    const { input, options = {} } = body;

    if (!input) {
      return NextResponse.json(
        { error: 'Input is required' },
        { status: 400 }
      );
    }

    // Get workflow
    const workflow = await workflowPersistence.getWorkflow(validation.workflowId, validation.userId);
    if (!workflow || !workflow.is_active) {
      return NextResponse.json(
        { error: 'Workflow not found or inactive' },
        { status: 404 }
      );
    }

    // Generate execution ID
    const executionId = crypto.randomUUID();

    // Start execution monitoring
    await workflowMonitor.startExecution(
      executionId,
      workflow.id,
      validation.userId,
      workflow.nodes.length
    );

    try {
      // Execute workflow
      const executor = WorkflowExecutor.getInstance();
      const result = await executor.executeWorkflow(
        workflow.id,
        validation.userId,
        workflow.nodes,
        workflow.edges,
        input
      );

      // Complete monitoring
      await workflowMonitor.completeExecution(executionId, result, Date.now());

      return NextResponse.json({
        success: true,
        execution_id: executionId,
        workflow_id: workflow.id,
        workflow_name: workflow.name,
        result,
        executed_at: new Date().toISOString(),
        execution_time_ms: Date.now()
      });

    } catch (executionError) {
      // Fail monitoring
      await workflowMonitor.failExecution(
        executionId,
        executionError instanceof Error ? executionError.message : 'Unknown execution error',
        { error: executionError }
      );

      throw executionError;
    }

  } catch (error) {
    console.error('Workflow execution API error:', error);
    
    return NextResponse.json({
      error: 'Workflow execution failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get API key from header
    const apiKey = request.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key required in X-API-Key header' },
        { status: 401 }
      );
    }

    // Validate API key
    const validation = await workflowPersistence.validateWorkflowAPIKey(apiKey);
    if (!validation.valid || !validation.workflowId || !validation.userId) {
      return NextResponse.json(
        { error: 'Invalid or expired API key' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('execution_id');

    if (executionId) {
      // Get specific execution status
      const execution = workflowMonitor.getExecutionStatus(executionId);
      if (!execution) {
        return NextResponse.json(
          { error: 'Execution not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        execution_id: executionId,
        status: execution.status,
        progress: execution.progress,
        current_node: execution.currentNodeId,
        logs: execution.logs,
        result: execution.result,
        error: execution.error,
        timestamp: execution.timestamp
      });
    } else {
      // Get workflow info and recent executions
      const workflow = await workflowPersistence.getWorkflow(validation.workflowId, validation.userId);
      if (!workflow) {
        return NextResponse.json(
          { error: 'Workflow not found' },
          { status: 404 }
        );
      }

      const recentExecutions = await workflowMonitor.getExecutionHistory(
        workflow.id,
        validation.userId,
        10
      );

      return NextResponse.json({
        workflow: {
          id: workflow.id,
          name: workflow.name,
          description: workflow.description,
          is_active: workflow.is_active,
          version: workflow.version,
          created_at: workflow.created_at,
          updated_at: workflow.updated_at
        },
        recent_executions: recentExecutions.map(exec => ({
          id: exec.id,
          status: exec.status,
          started_at: exec.started_at,
          completed_at: exec.completed_at,
          execution_time_ms: exec.execution_time_ms,
          nodes_executed: exec.nodes_executed,
          nodes_total: exec.nodes_total
        }))
      });
    }

  } catch (error) {
    console.error('Workflow status API error:', error);
    
    return NextResponse.json({
      error: 'Failed to get workflow status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
