/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/tools/notion/authorize/route";
exports.ids = ["app/api/auth/tools/notion/authorize/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_auth_tools_notion_authorize_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/tools/notion/authorize/route.ts */ \"(rsc)/./src/app/api/auth/tools/notion/authorize/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/tools/notion/authorize/route\",\n        pathname: \"/api/auth/tools/notion/authorize\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/tools/notion/authorize/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\auth\\\\tools\\\\notion\\\\authorize\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_auth_tools_notion_authorize_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/tools/notion/authorize/route.ts":
/*!**********************************************************!*\
  !*** ./src/app/api/auth/tools/notion/authorize/route.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_oauth_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/oauth/config */ \"(rsc)/./src/lib/oauth/config.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_3__);\n// Notion OAuth Authorization Endpoint\n// Initiates OAuth flow for Notion integration\n\n\n\n\nasync function GET(request) {\n    try {\n        console.log('🔐 NOTION OAUTH: Authorization request started');\n        // Get authenticated user\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('🔐 NOTION OAUTH: User not authenticated:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        // Get return URL from query params\n        const { searchParams } = new URL(request.url);\n        const returnUrl = searchParams.get('returnUrl') || '/manual-build';\n        console.log(`🔐 NOTION OAUTH: Authorizing Notion for user ${user.id}`);\n        // Generate state parameter for CSRF protection\n        const state = crypto__WEBPACK_IMPORTED_MODULE_3___default().randomBytes(32).toString('hex');\n        // Store state in database for verification\n        const { error: stateError } = await supabase.from('oauth_states').insert({\n            state,\n            user_id: user.id,\n            tool_type: 'notion',\n            return_url: returnUrl,\n            expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes\n        });\n        if (stateError) {\n            console.error('🔐 NOTION OAUTH: Error storing state:', stateError);\n        // Continue anyway, state verification is optional\n        }\n        // Generate authorization URL\n        const authUrl = (0,_lib_oauth_config__WEBPACK_IMPORTED_MODULE_2__.generateAuthUrl)('notion', state);\n        if (!authUrl) {\n            console.error('🔐 NOTION OAUTH: Failed to generate auth URL');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'OAuth configuration error'\n            }, {\n                status: 500\n            });\n        }\n        console.log('🔐 NOTION OAUTH: Redirecting to Notion authorization');\n        // Return the authorization URL for client-side redirect\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            authUrl,\n            state,\n            toolType: 'notion'\n        });\n    } catch (error) {\n        console.error('🔐 NOTION OAUTH: Authorization error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'OAuth authorization failed'\n        }, {\n            status: 500\n        });\n    }\n}\n// Handle POST requests for programmatic authorization\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { returnUrl } = body;\n        // Create a new request with query parameters\n        const url = new URL(request.url);\n        if (returnUrl) {\n            url.searchParams.set('returnUrl', returnUrl);\n        }\n        const newRequest = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(url, {\n            method: 'GET',\n            headers: request.headers\n        });\n        return GET(newRequest);\n    } catch (error) {\n        console.error('🔐 NOTION OAUTH: POST authorization error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid request body'\n        }, {\n            status: 400\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/tools/notion/authorize/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/oauth/config.ts":
/*!*********************************!*\
  !*** ./src/lib/oauth/config.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TOOL_DESCRIPTIONS: () => (/* binding */ TOOL_DESCRIPTIONS),\n/* harmony export */   TOOL_DISPLAY_NAMES: () => (/* binding */ TOOL_DISPLAY_NAMES),\n/* harmony export */   TOOL_ICONS: () => (/* binding */ TOOL_ICONS),\n/* harmony export */   generateAuthUrl: () => (/* binding */ generateAuthUrl),\n/* harmony export */   getOAuthConfigForTool: () => (/* binding */ getOAuthConfigForTool),\n/* harmony export */   getToolOAuthConfigs: () => (/* binding */ getToolOAuthConfigs),\n/* harmony export */   validateOAuthConfig: () => (/* binding */ validateOAuthConfig)\n/* harmony export */ });\n// OAuth Configuration for Tool Integrations\n// This file contains OAuth configurations for all supported tools\n// Get the base URL for redirects\nconst getBaseUrl = ()=>{\n    if (false) {}\n    // Server-side detection\n    if (false) {}\n    return process.env.NEXTAUTH_URL || 'http://localhost:3000';\n};\n// Google OAuth configuration for tools\nconst getGoogleToolsConfig = ()=>({\n        clientId: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_ID || process.env.GOOGLE_CLIENT_ID || '',\n        clientSecret: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET || '',\n        authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',\n        tokenUrl: 'https://oauth2.googleapis.com/token',\n        scopes: [\n            'https://www.googleapis.com/auth/drive',\n            'https://www.googleapis.com/auth/documents',\n            'https://www.googleapis.com/auth/spreadsheets',\n            'https://www.googleapis.com/auth/gmail.modify',\n            'https://www.googleapis.com/auth/calendar',\n            'https://www.googleapis.com/auth/youtube'\n        ],\n        redirectUri: process.env.GOOGLE_TOOLS_OAUTH_REDIRECT_URI || `${getBaseUrl()}/api/auth/tools/google/callback`,\n        additionalParams: {\n            access_type: 'offline',\n            prompt: 'consent',\n            include_granted_scopes: 'true'\n        }\n    });\n// Notion OAuth configuration\nconst getNotionConfig = ()=>({\n        clientId: process.env.NOTION_OAUTH_CLIENT_ID || '',\n        clientSecret: process.env.NOTION_OAUTH_CLIENT_SECRET || '',\n        authorizationUrl: 'https://api.notion.com/v1/oauth/authorize',\n        tokenUrl: 'https://api.notion.com/v1/oauth/token',\n        scopes: [],\n        redirectUri: process.env.NOTION_OAUTH_REDIRECT_URI || `${getBaseUrl()}/api/auth/tools/notion/callback`,\n        additionalParams: {\n            owner: 'user',\n            response_type: 'code'\n        }\n    });\n// Tool-specific OAuth configurations\nconst getToolOAuthConfigs = ()=>{\n    const googleConfig = getGoogleToolsConfig();\n    return {\n        // Google services all use the same OAuth config with different scopes\n        google_drive: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/drive'\n            ]\n        },\n        google_docs: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/documents'\n            ]\n        },\n        google_sheets: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/spreadsheets'\n            ]\n        },\n        gmail: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/gmail.modify'\n            ]\n        },\n        calendar: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/calendar'\n            ]\n        },\n        youtube: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/youtube'\n            ]\n        },\n        notion: getNotionConfig(),\n        supabase: {\n            clientId: '',\n            clientSecret: '',\n            authorizationUrl: '',\n            tokenUrl: '',\n            scopes: [],\n            redirectUri: '',\n            additionalParams: {}\n        }\n    };\n};\n// Get OAuth config for a specific tool\nconst getOAuthConfigForTool = (toolType)=>{\n    const configs = getToolOAuthConfigs();\n    return configs[toolType] || null;\n};\n// Validate OAuth configuration\nconst validateOAuthConfig = (config)=>{\n    return !!(config.clientId && config.clientSecret && config.authorizationUrl && config.tokenUrl && config.redirectUri);\n};\n// Generate OAuth authorization URL\nconst generateAuthUrl = (toolType, state)=>{\n    const config = getOAuthConfigForTool(toolType);\n    if (!config || !validateOAuthConfig(config)) {\n        return null;\n    }\n    const params = new URLSearchParams({\n        client_id: config.clientId,\n        redirect_uri: config.redirectUri,\n        response_type: 'code',\n        scope: config.scopes.join(' '),\n        state,\n        ...config.additionalParams\n    });\n    return `${config.authorizationUrl}?${params.toString()}`;\n};\n// Tool display names\nconst TOOL_DISPLAY_NAMES = {\n    google_drive: 'Google Drive',\n    google_docs: 'Google Docs',\n    google_sheets: 'Google Sheets',\n    gmail: 'Gmail',\n    calendar: 'Google Calendar',\n    youtube: 'YouTube',\n    notion: 'Notion',\n    supabase: 'Supabase'\n};\n// Tool icons/emojis\nconst TOOL_ICONS = {\n    google_drive: '📁',\n    google_docs: '📄',\n    google_sheets: '📊',\n    gmail: '📧',\n    calendar: '📅',\n    youtube: '📺',\n    notion: '📝',\n    supabase: '🗄️'\n};\n// Tool descriptions\nconst TOOL_DESCRIPTIONS = {\n    google_drive: 'Access and manage Google Drive files',\n    google_docs: 'Create and edit Google Documents',\n    google_sheets: 'Work with Google Spreadsheets',\n    gmail: 'Send and manage emails',\n    calendar: 'Manage calendar events and schedules',\n    youtube: 'Access YouTube data and analytics',\n    notion: 'Access Notion databases and pages',\n    supabase: 'Direct database operations'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/oauth/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fnotion%2Fauthorize%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();