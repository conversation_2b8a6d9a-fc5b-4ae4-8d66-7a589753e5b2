// OAuth Configuration for Tool Integrations
// This file contains OAuth configurations for all supported tools

export interface OAuthConfig {
  clientId: string;
  clientSecret: string;
  authorizationUrl: string;
  tokenUrl: string;
  scopes: string[];
  redirectUri: string;
  additionalParams?: Record<string, string>;
}

export interface ToolOAuthConfigs {
  [key: string]: OAuthConfig;
}

// Get the base URL for redirects
const getBaseUrl = (): string => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  
  // Server-side detection
  if (process.env.NODE_ENV === 'production') {
    return 'https://roukey.online';
  }
  
  return process.env.NEXTAUTH_URL || 'http://localhost:3000';
};

// Google OAuth configuration for tools
const getGoogleToolsConfig = (): OAuthConfig => ({
  clientId: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_ID || process.env.GOOGLE_CLIENT_ID || '',
  clientSecret: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET || '',
  authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
  tokenUrl: 'https://oauth2.googleapis.com/token',
  scopes: [
    'https://www.googleapis.com/auth/drive',
    'https://www.googleapis.com/auth/documents',
    'https://www.googleapis.com/auth/spreadsheets',
    'https://www.googleapis.com/auth/gmail.modify',
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/youtube'
  ],
  redirectUri: process.env.GOOGLE_TOOLS_OAUTH_REDIRECT_URI || `${getBaseUrl()}/api/auth/tools/google/callback`,
  additionalParams: {
    access_type: 'offline',
    prompt: 'consent',
    include_granted_scopes: 'true'
  }
});

// Notion OAuth configuration
const getNotionConfig = (): OAuthConfig => ({
  clientId: process.env.NOTION_OAUTH_CLIENT_ID || '',
  clientSecret: process.env.NOTION_OAUTH_CLIENT_SECRET || '',
  authorizationUrl: 'https://api.notion.com/v1/oauth/authorize',
  tokenUrl: 'https://api.notion.com/v1/oauth/token',
  scopes: [], // Notion uses capabilities instead of scopes
  redirectUri: process.env.NOTION_OAUTH_REDIRECT_URI || `${getBaseUrl()}/api/auth/tools/notion/callback`,
  additionalParams: {
    owner: 'user',
    response_type: 'code'
  }
});

// Tool-specific OAuth configurations
export const getToolOAuthConfigs = (): ToolOAuthConfigs => {
  const googleConfig = getGoogleToolsConfig();
  
  return {
    // Google services all use the same OAuth config with different scopes
    google_drive: {
      ...googleConfig,
      scopes: ['https://www.googleapis.com/auth/drive']
    },
    google_docs: {
      ...googleConfig,
      scopes: ['https://www.googleapis.com/auth/documents']
    },
    google_sheets: {
      ...googleConfig,
      scopes: ['https://www.googleapis.com/auth/spreadsheets']
    },
    gmail: {
      ...googleConfig,
      scopes: ['https://www.googleapis.com/auth/gmail.modify']
    },
    calendar: {
      ...googleConfig,
      scopes: ['https://www.googleapis.com/auth/calendar']
    },
    youtube: {
      ...googleConfig,
      scopes: ['https://www.googleapis.com/auth/youtube']
    },
    notion: getNotionConfig(),
    supabase: {
      clientId: '',
      clientSecret: '',
      authorizationUrl: '',
      tokenUrl: '',
      scopes: [],
      redirectUri: '',
      additionalParams: {}
    }
  };
};

// Get OAuth config for a specific tool
export const getOAuthConfigForTool = (toolType: string): OAuthConfig | null => {
  const configs = getToolOAuthConfigs();
  return configs[toolType] || null;
};

// Validate OAuth configuration
export const validateOAuthConfig = (config: OAuthConfig): boolean => {
  return !!(
    config.clientId &&
    config.clientSecret &&
    config.authorizationUrl &&
    config.tokenUrl &&
    config.redirectUri
  );
};

// Generate OAuth authorization URL
export const generateAuthUrl = (toolType: string, state: string): string | null => {
  const config = getOAuthConfigForTool(toolType);
  if (!config || !validateOAuthConfig(config)) {
    return null;
  }

  const params = new URLSearchParams({
    client_id: config.clientId,
    redirect_uri: config.redirectUri,
    response_type: 'code',
    scope: config.scopes.join(' '),
    state,
    ...config.additionalParams
  });

  return `${config.authorizationUrl}?${params.toString()}`;
};

// Tool display names
export const TOOL_DISPLAY_NAMES: Record<string, string> = {
  google_drive: 'Google Drive',
  google_docs: 'Google Docs',
  google_sheets: 'Google Sheets',
  gmail: 'Gmail',
  calendar: 'Google Calendar',
  youtube: 'YouTube',
  notion: 'Notion',
  supabase: 'Supabase'
};

// Tool icons/emojis
export const TOOL_ICONS: Record<string, string> = {
  google_drive: '📁',
  google_docs: '📄',
  google_sheets: '📊',
  gmail: '📧',
  calendar: '📅',
  youtube: '📺',
  notion: '📝',
  supabase: '🗄️'
};

// Tool descriptions
export const TOOL_DESCRIPTIONS: Record<string, string> = {
  google_drive: 'Access and manage Google Drive files',
  google_docs: 'Create and edit Google Documents',
  google_sheets: 'Work with Google Spreadsheets',
  gmail: 'Send and manage emails',
  calendar: 'Manage calendar events and schedules',
  youtube: 'Access YouTube data and analytics',
  notion: 'Access Notion databases and pages',
  supabase: 'Direct database operations'
};
